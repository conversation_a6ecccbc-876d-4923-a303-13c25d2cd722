uuid: a520d638-45e2-4ff7-8cad-8c86ad207c2b
langcode: fr
status: false
dependencies:
  config:
    - core.entity_view_mode.node.teaser
  module:
    - node
    - user
_core:
  default_config_hash: exIF08PTvYSK6tJCsAzBqMEKUBKIptPorMn74SVhevc
id: archive
label: Archiver
module: node
description: 'Tous les contenus, triés par mois.'
tag: default
base_table: node_field_data
base_field: nid
display:
  default:
    id: default
    display_title: 'Par défaut'
    display_plugin: default
    position: 0
    display_options:
      title: 'Archive mensuelle'
      fields: {  }
      pager:
        type: mini
        options:
          offset: 0
          pagination_heading_level: h4
          items_per_page: 10
          total_pages: 0
          id: 0
          tags:
            next: ››
            previous: ‹‹
          expose:
            items_per_page: false
            items_per_page_label: 'Éléments par page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- Tout -'
            offset: false
            offset_label: Décalage
      exposed_form:
        type: basic
        options:
          submit_button: Appliquer
          reset_button: false
          reset_button_label: Réinitialiser
          exposed_sorts_label: 'Trier par'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: perm
        options:
          perm: 'access content'
      cache:
        type: tag
        options: {  }
      empty: {  }
      sorts:
        created:
          id: created
          table: node_field_data
          field: created
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: created
          plugin_id: date
          order: DESC
          expose:
            label: ''
            field_identifier: created
          exposed: false
          granularity: second
      arguments:
        created_year_month:
          id: created_year_month
          table: node_field_data
          field: created_year_month
          entity_type: node
          plugin_id: date_year_month
          default_action: summary
          exception:
            title_enable: true
          title_enable: true
          title: '{{ arguments.created_year_month }}'
          default_argument_type: fixed
          summary_options:
            override: true
            items_per_page: 30
          summary:
            sort_order: desc
            format: default_summary
          specify_validation: true
      filters:
        status:
          id: status
          table: node_field_data
          field: status
          entity_type: node
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 0
          expose:
            operator: '0'
            operator_limit_selection: false
            operator_list: {  }
        langcode:
          id: langcode
          table: node_field_data
          field: langcode
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: langcode
          plugin_id: language
          operator: in
          value:
            '***LANGUAGE_language_content***': '***LANGUAGE_language_content***'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      style:
        type: default
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
          uses_fields: false
      row:
        type: 'entity:node'
        options:
          view_mode: teaser
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships: {  }
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
  block_1:
    id: block_1
    display_title: Bloc
    display_plugin: block
    position: 1
    display_options:
      arguments:
        created_year_month:
          id: created_year_month
          table: node_field_data
          field: created_year_month
          entity_type: node
          plugin_id: date_year_month
          default_action: summary
          exception:
            title_enable: true
          title_enable: true
          title: '{{ arguments.created_year_month }}'
          default_argument_type: fixed
          summary_options:
            items_per_page: 30
          summary:
            format: default_summary
          specify_validation: true
      query:
        type: views_query
        options: {  }
      defaults:
        arguments: false
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
  page_1:
    id: page_1
    display_title: Page
    display_plugin: page
    position: 2
    display_options:
      query:
        type: views_query
        options: {  }
      display_extenders: {  }
      path: archive
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
